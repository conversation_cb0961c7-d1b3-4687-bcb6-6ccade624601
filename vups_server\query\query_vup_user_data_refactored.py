"""
Refactored user data query module.
This module provides a unified interface to the new modular query services.
Maintains backward compatibility while using the optimized modular structure.
"""

import json
import re
import hashlib
import os
from collections import defaultdict
from datetime import datetime, timedelta
from statistics import mean
from typing import Dict, List, Optional, Union

import asyncpg
from click import Choice

import vups.utils as U
from vups.base.schema.data_class import VupRencentInfo
from vups.logger import logger
from vups.utils.bi_utils import word_cloud_gen
from vups_server.query.query_live_info_data import query_now_live_info_by_room
from vups_server.sql.db_pool import get_connection
from vups_server.sql.sentence import user_sql
from vups_server.config.const import VUP_SERVER_ROOT

# Import new modular services
from vups_server.query.user_statistics import user_stats_service
from vups_server.query.content_queries import content_service
from vups_server.query.analytics import analytics_service
from vups_server.query.specialized_queries import specialized_service


# ============================================================================
# BACKWARD COMPATIBILITY FUNCTIONS
# These functions maintain the original API while using the new modular services
# ============================================================================

async def query_current_stat_by_mid(uid: str):
    """
    查询指定uid主播的最新数据。
    Query the latest data for a specific user ID from the current_stat_table.

    Args:
        uid: 用户 UID。

    Returns:
        asyncpg.Record or None: 包含最新统计数据的记录，如果查询失败或无数据则返回 None。
    """
    return await user_stats_service.get_current_stat_by_uid(uid)


async def query_peroid_user_all_stat_by_uid_and_time(
    uid: str, start_time: str, end_time: str
):
    """
    查询指定时间段内用户的所有统计数据。
    Query all statistics for a user within a specific time period.

    Args:
        uid: 用户 UID。
        start_time: 开始时间字符串 (YYYY-MM-DD)。
        end_time: 结束时间字符串 (YYYY-MM-DD)。

    Returns:
        List[List]: 格式化的统计数据列表。
    """
    return await user_stats_service.get_user_stats_by_time_range(uid, start_time, end_time)


async def query_whole_user_all_stat_by_uid_and_recent(uid: str, recent_days: int = -1):
    """
    查询用户最近指定天数的所有统计数据。
    Query all statistics for a user for recent days.

    Args:
        uid: 用户 UID。
        recent_days: 最近天数，-1 表示所有数据。

    Returns:
        List[List]: 格式化的统计数据列表。
    """
    return await user_stats_service.get_user_stats_recent_days(uid, recent_days)


async def query_whole_user_follower_num_by_mid_and_recent(uid: str, recent_days: int = -1):
    """
    查询用户最近指定天数的粉丝数历史。
    Query follower number history for a user for recent days.

    Args:
        uid: 用户 UID。
        recent_days: 最近天数，-1 表示所有数据。

    Returns:
        List[List]: 粉丝数历史数据。
    """
    return await user_stats_service.get_follower_history(uid, recent_days)


async def query_now_user_follower_num_by_mid(uid: str):
    """
    查询用户当前粉丝数。
    Query current follower count for a user.

    Args:
        uid: 用户 UID。

    Returns:
        int: 当前粉丝数，如果查询失败则返回 -1。
    """
    return await user_stats_service.get_current_follower_count(uid)


async def query_whole_dahanghai_num_by_mid_and_recent(uid: str, recent_days: int = -1):
    """
    查询用户最近指定天数的大航海数历史。
    Query dahanghai number history for a user for recent days.

    Args:
        uid: 用户 UID。
        recent_days: 最近天数，-1 表示所有数据。

    Returns:
        List[List]: 大航海数历史数据。
    """
    return await user_stats_service.get_dahanghai_history(uid, recent_days)


async def query_now_user_dahanghai_num_by_mid(uid: str):
    """
    查询用户当前大航海数。
    Query current dahanghai count for a user.

    Args:
        uid: 用户 UID。

    Returns:
        int: 当前大航海数，如果查询失败则返回 -1。
    """
    return await user_stats_service.get_current_dahanghai_count(uid)


async def query_user_info_by_mid(uid: str):
    """
    查询用户基本信息。
    Query user's basic information by UID.

    Args:
        uid: 用户 UID。

    Returns:
        asyncpg.Record or None: 用户信息记录。
    """
    return await content_service.get_user_info_by_uid(uid)


async def calculate_dahanghai_rate_by_mid(uid: str, recent_days: int = 90):
    """
    计算用户大航海增长率。
    Calculate dahanghai growth rate for a user.

    Args:
        uid: 用户 UID。
        recent_days: 计算时间窗口（天数）。

    Returns:
        str: 格式化的增长率字符串。
    """
    return await user_stats_service.calculate_dahanghai_growth_rate(uid, recent_days)


async def calculate_follower_rate_by_mid(uid: str, recent_days: int = 90):
    """
    计算用户粉丝增长率。
    Calculate follower growth rate for a user.

    Args:
        uid: 用户 UID。
        recent_days: 计算时间窗口（天数）。

    Returns:
        str: 格式化的增长率字符串。
    """
    return await user_stats_service.calculate_follower_growth_rate(uid, recent_days)


async def query_user_dynamics_by_mid(uid: str):
    """
    查询用户所有动态。
    Query all dynamics for a given user with heat calculation.

    Args:
        uid: 用户 UID。

    Returns:
        List[Dict]: 包含热度值的动态列表。
    """
    return await content_service.get_user_dynamics(uid)


async def query_all_video_list_by_mid(uid: str):
    """
    查询用户所有视频。
    Query all videos for a given user with heat calculation.

    Args:
        uid: 用户 UID。

    Returns:
        List[Dict]: 包含热度值的视频列表。
    """
    return await content_service.get_user_videos(uid)


async def query_current_videos(uid: str):
    """
    查询用户最新视频。
    Query the most recent video for a given user.

    Args:
        uid: 用户 UID。

    Returns:
        List: 最新视频信息列表。
    """
    return await content_service.get_latest_video(uid)


async def query_current_dynamics(uid: str):
    """
    查询用户最新动态。
    Query the most recent dynamic for a given user.

    Args:
        uid: 用户 UID。

    Returns:
        List: 最新动态信息列表。
    """
    return await content_service.get_latest_dynamic(uid)


async def query_top_n_comments(uid: str, limit: int = 10):
    """
    查询用户热门评论。
    Query top comments for a user with enhanced caching.

    Args:
        uid: 用户 UID。
        limit: 返回评论数量限制。

    Returns:
        List[Dict]: 热门评论列表。
    """
    return await analytics_service.get_top_comments(uid, limit)


async def query_top_n_comments_user(uid: str, limit: int = 10):
    """
    查询用户热门评论用户。
    Query top comment users for a user with enhanced caching.

    Args:
        uid: 用户 UID。
        limit: 返回用户数量限制。

    Returns:
        List[Dict]: 热门评论用户列表。
    """
    return await analytics_service.get_top_comment_users(uid, limit)


async def query_top_n_videos(uid: str, limit: int = 10):
    """
    查询用户热门视频。
    Query top videos for a user based on heat score.

    Args:
        uid: 用户 UID。
        limit: 返回视频数量限制。

    Returns:
        List[Dict]: 热门视频列表。
    """
    return await analytics_service.get_top_videos(uid, limit)


async def query_top_n_dynamics(uid: str, limit: int = 10):
    """
    查询用户热门动态。
    Query top dynamics for a user based on heat score.

    Args:
        uid: 用户 UID。
        limit: 返回动态数量限制。

    Returns:
        List[Dict]: 热门动态列表。
    """
    return await analytics_service.get_top_dynamics(uid, limit)


async def query_recent_top_n_videos(uid: str, limit: int = 10):
    """
    查询用户最近热门视频。
    Query recent top videos for a user based on heat.

    Args:
        uid: 用户 UID。
        limit: 返回视频数量限制。

    Returns:
        List[Dict]: 最近热门视频列表。
    """
    return await content_service.get_recent_top_videos(uid, limit)


async def query_comments_for_wordcloud(uid: str, limit: int = 1000):
    """
    查询用于词云生成的评论。
    Query comments for word cloud generation.

    Args:
        uid: 用户 UID。
        limit: 返回评论数量限制。

    Returns:
        List[str]: 评论内容列表。
    """
    return await content_service.get_comments_for_wordcloud(uid, limit)


async def query_tieba_whole(uid: str):
    """
    查询用户贴吧数据。
    Query comprehensive tieba data for a user.

    Args:
        uid: 用户 UID。

    Returns:
        Dict: 贴吧统计和数据字典。
    """
    return await specialized_service.get_tieba_data(uid)


async def query_tieba_threads(uid: str):
    """
    查询用户贴吧帖子。
    Query tieba threads for a user.

    Args:
        uid: 用户 UID。

    Returns:
        List[Dict]: 贴吧帖子列表。
    """
    tieba_data = await specialized_service.get_tieba_data(uid)
    return tieba_data.get("threads", [])


async def query_all_video_comments_by_mid(uid: str):
    """
    查询用户所有视频评论。
    Query all video comments for a user.

    Args:
        uid: 用户 UID。

    Returns:
        List[Dict]: 视频评论列表。
    """
    return await content_service.get_video_comments(uid)


async def query_all_dynamics_comments_by_mid(uid: str):
    """
    查询用户所有动态评论。
    Query all dynamics comments for a user.

    Args:
        uid: 用户 UID。

    Returns:
        List[Dict]: 动态评论列表。
    """
    return await content_service.get_dynamics_comments(uid)


async def query_recent_relationships(uid: str, limit: int = 10):
    """
    查询最近关系数据。
    Query recent relationships from AI-generated data.

    Args:
        uid: 用户 UID。
        limit: 返回关系数量限制。

    Returns:
        List[Dict]: 最近关系列表。
    """
    return await analytics_service.get_recent_relationships(uid, limit)


async def query_tieba_summaries_from_ai_gen_table(uid: str, limit: int = 5):
    """
    查询贴吧摘要。
    Query tieba summaries from AI-generated data.

    Args:
        uid: 用户 UID。
        limit: 返回摘要数量限制。

    Returns:
        List[Dict]: 贴吧摘要列表。
    """
    return await analytics_service.get_tieba_summaries(uid, limit)


async def query_rise_reason_from_ai_gen_table(uid: str, limit: int = 5):
    """
    查询上升原因。
    Query rise reasons from AI-generated data.

    Args:
        uid: 用户 UID。
        limit: 返回原因数量限制。

    Returns:
        List[Dict]: 上升原因列表。
    """
    return await analytics_service.get_rise_reasons(uid, limit)


async def query_latest_fans_medal_rank(uid: str):
    """
    查询最新粉丝勋章排行。
    Query latest fans medal rank data.

    Args:
        uid: 用户 UID。

    Returns:
        List[Dict]: 粉丝勋章排行数据。
    """
    return await analytics_service.get_fans_medal_rank(uid)


async def query_fans_medal_rank_by_datetime(uid: str, target_datetime: datetime):
    """
    查询指定时间的粉丝勋章排行。
    Query fans medal rank data by specific datetime.

    Args:
        uid: 用户 UID。
        target_datetime: 目标时间。

    Returns:
        List[Dict]: 粉丝勋章排行数据。
    """
    return await analytics_service.get_fans_medal_rank(uid, target_datetime)


async def query_video_ai_conclusion_by_bvid(bvid: str):
    """
    查询视频AI结论。
    Query AI conclusion for a specific video.

    Args:
        bvid: 视频BVID。

    Returns:
        str or None: AI结论文本。
    """
    return await analytics_service.get_video_ai_conclusion(bvid)


async def query_current_follower_change_num(uid: str, recent_days: int = 1):
    """
    查询粉丝变化数。
    Calculate follower change over specified days.

    Args:
        uid: 用户 UID。
        recent_days: 回溯天数。

    Returns:
        int or None: 粉丝变化数。
    """
    return await user_stats_service.get_follower_change(uid, recent_days)


async def query_current_dahanghai_change_num(uid: str, recent_days: int = 1):
    """
    查询大航海变化数。
    Calculate dahanghai change over specified days.

    Args:
        uid: 用户 UID。
        recent_days: 回溯天数。

    Returns:
        int or None: 大航海变化数。
    """
    return await user_stats_service.get_dahanghai_change(uid, recent_days)


async def query_latest_dahanghai_list_by_uid(uid: str):
    """
    查询最新大航海列表。
    Query latest dahanghai list for a user.

    Args:
        uid: 用户 UID。

    Returns:
        List[Dict]: 大航海列表数据。
    """
    return await specialized_service.get_dahanghai_list(uid)


async def query_dahanghai_list_by_uid_and_datetime(uid: str, target_datetime: datetime):
    """
    查询指定时间的大航海列表。
    Query dahanghai list by specific datetime.

    Args:
        uid: 用户 UID。
        target_datetime: 目标时间。

    Returns:
        List[Dict]: 大航海列表数据。
    """
    return await specialized_service.get_dahanghai_list(uid, target_datetime)


async def query_followers_list(uid: str, target_datetime: Optional[datetime] = None):
    """
    查询粉丝列表。
    Query followers list for a user.

    Args:
        uid: 用户 UID。
        target_datetime: 目标时间（可选）。

    Returns:
        List[Dict]: 粉丝列表数据。
    """
    return await specialized_service.get_followers_list(uid, target_datetime)


async def query_followers_review_list(uid: str):
    """
    查询粉丝审核列表。
    Query followers review data.

    Args:
        uid: 用户 UID。

    Returns:
        Dict: 粉丝审核数据和比率。
    """
    return await specialized_service.get_followers_review_data(uid)


async def query_followers_review_rate(uid: str):
    """
    查询粉丝审核比率。
    Query followers review rate.

    Args:
        uid: 用户 UID。

    Returns:
        float: 粉丝审核比率。
    """
    review_data = await specialized_service.get_followers_review_data(uid)
    return review_data.get("review_rate", 0.0)


async def query_recent_comments_sentiment_value(uid: str, recent_days: int = 30):
    """
    查询最近评论情感值。
    Query recent comments sentiment value.

    Args:
        uid: 用户 UID。
        recent_days: 最近天数。

    Returns:
        float or None: 平均情感值。
    """
    return await analytics_service.get_recent_comments_sentiment(uid, recent_days)


async def query_comment_wordcloud(uid: str, limit: int = 1000):
    """
    生成评论词云。
    Generate word cloud from user comments.

    Args:
        uid: 用户 UID。
        limit: 处理评论数量限制。

    Returns:
        str or None: 生成的词云图片路径。
    """
    return await specialized_service.generate_comment_wordcloud(uid, limit)


async def cleanup_old_wordcloud_files():
    """
    清理旧的词云文件。
    Clean up old word cloud files.

    Returns:
        int: 删除的文件数量。
    """
    return await specialized_service.cleanup_old_wordcloud_files()


# ============================================================================
# ENHANCED FUNCTIONS
# These functions provide new capabilities using the modular services
# ============================================================================

async def query_recent_info(uid: str):
    """
    查询用户综合信息。
    Get comprehensive user information combining multiple data sources.

    Args:
        uid: 用户 UID。

    Returns:
        Dict: 综合用户信息字典。
    """
    return await specialized_service.get_comprehensive_user_info(uid)


async def query_recent_info_with_view(uid: str):
    """
    查询用户综合信息（包含视图数据）。
    Get comprehensive user information with additional view data.

    Args:
        uid: 用户 UID。

    Returns:
        Dict: 包含视图数据的综合用户信息。
    """
    comprehensive_info = await specialized_service.get_comprehensive_user_info(uid)

    # Add video day data for recent videos
    if comprehensive_info.get("top_content", {}).get("videos"):
        top_video = comprehensive_info["top_content"]["videos"][0]
        if top_video and top_video.get("bvid"):
            video_day_data = await specialized_service.get_video_day_data(
                uid, top_video["bvid"], 30
            )
            comprehensive_info["video_day_data"] = video_day_data

    return comprehensive_info


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================

def get_service_instances():
    """
    获取服务实例。
    Get all service instances for direct access.

    Returns:
        Dict: 服务实例字典。
    """
    return {
        "user_stats": user_stats_service,
        "content": content_service,
        "analytics": analytics_service,
        "specialized": specialized_service,
    }
